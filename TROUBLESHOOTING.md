# Neo4j AuraDB Connection Troubleshooting Guide

## 🔍 Current Issue
Your Neo4j AuraDB instance appears to be running in the Neo4j Desktop, but the hostname `560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io` is not resolving via DNS.

## 🚨 Error Details
```
❌ DNS Resolution failed: [Errno 11001] getaddrinfo failed
❌ Cannot resolve address 560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io:7687
```

## 🔧 Step-by-Step Solutions

### 1. 📡 Check Instance Status in Neo4j Aura Console

**Action Required:**
1. Go to [Neo4j Aura Console](https://console.neo4j.io/)
2. Log in with your Neo4j account
3. Check if the instance `560ecc8f-0dc9-4e02-92ea-d1e600d02c3d` is:
   - ✅ **Running** (green status)
   - ⏸️ **Paused** (yellow status) 
   - ❌ **Stopped** (red status)

**If Paused/Stopped:**
- Click "Resume" or "Start" button
- Wait 2-3 minutes for full startup
- The hostname should become resolvable

### 2. 🔄 Get Fresh Connection Details

**Action Required:**
1. In Neo4j Aura Console, click on your instance
2. Go to "Connect" tab
3. Copy the **exact** connection URI
4. Verify it matches: `neo4j+s://560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io`

**Common Issues:**
- URI might have changed
- Instance might be in different region
- Typo in the instance ID

### 3. 🌐 Test Network Connectivity

**Try these commands:**
```bash
# Test DNS resolution
nslookup 560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io

# Test with different DNS server
nslookup 560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io *******

# Test connectivity
telnet 560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io 7687
```

### 4. 🔒 Check Firewall/Network Settings

**Corporate Network Issues:**
- Check if your company firewall blocks port 7687
- Try from a different network (mobile hotspot)
- Contact IT department about Neo4j AuraDB access

**Windows Firewall:**
- Ensure outbound connections on port 7687 are allowed
- Try temporarily disabling Windows Firewall for testing

### 5. 🔐 Verify Credentials

**Check your .env file:**
```env
NEO4J_URI=neo4j+s://560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=12345678
NEO4J_DATABASE=neo4j
```

**Common Issues:**
- Username might not be "neo4j" (check Aura console)
- Password might be incorrect
- Database name might be different

### 6. 🕒 Wait for Instance Warmup

**AuraDB Auto-Pause:**
- Instances auto-pause after 3 days of inactivity
- Startup can take 2-5 minutes
- DNS resolution may lag behind instance startup

**Action:**
1. Wait 5 minutes after starting the instance
2. Try connection again
3. Check Aura console for "Running" status

### 7. 🔄 Alternative Connection Methods

**Try Neo4j Browser First:**
1. In Aura console, click "Open with Neo4j Browser"
2. If this works, the instance is running correctly
3. The issue might be with Python driver or local DNS

**Use Different Connection String:**
```python
# Try with explicit port
NEO4J_URI=neo4j+s://560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io:7687

# Try without SSL (not recommended for production)
NEO4J_URI=neo4j://560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io:7687
```

## 🧪 Test Scripts

### Quick Connection Test
```bash
# Run our diagnostic script
python test_connection.py

# Try the demo with better error handling
python demo.py
```

### Manual Python Test
```python
from neo4j import GraphDatabase

uri = "neo4j+s://560ecc8f-0dc9-4e02-92ea-d1e600d02c3d.databases.neo4j.io"
driver = GraphDatabase.driver(uri, auth=("neo4j", "12345678"))

try:
    driver.verify_connectivity()
    print("✅ Connection successful!")
except Exception as e:
    print(f"❌ Connection failed: {e}")
finally:
    driver.close()
```

## 📞 When to Contact Support

**Contact Neo4j Support if:**
- Instance shows "Running" but hostname doesn't resolve after 10 minutes
- You can access via Neo4j Browser but not via Python driver
- Error persists across different networks
- Instance was working before and suddenly stopped

**Include in Support Request:**
- Instance ID: `560ecc8f-0dc9-4e02-92ea-d1e600d02c3d`
- Error message: "Cannot resolve address"
- Your location/region
- Network environment (corporate/home/<USER>

## 🚀 Once Connected

**After fixing the connection:**
1. Run: `python test_connection.py` (should show ✅)
2. Run: `python load_sample_data.py` (loads test data)
3. Run: `python demo.py` (full application test)
4. Start using the patent similarity search!

## 📋 Checklist

- [ ] Check instance status in Aura console
- [ ] Verify connection URI is correct
- [ ] Test from different network
- [ ] Wait 5+ minutes after starting instance
- [ ] Try Neo4j Browser connection
- [ ] Check firewall settings
- [ ] Verify credentials
- [ ] Contact support if needed

## 💡 Alternative: Local Neo4j

**If AuraDB continues to have issues:**
1. Install Neo4j Desktop locally
2. Create a local database
3. Update .env to use: `NEO4J_URI=neo4j://localhost:7687`
4. Load sample data and test locally

This will let you use the application immediately while troubleshooting the cloud connection.
