#!/usr/bin/env python3
"""
Check Neo4j Desktop setup and provide guidance.
"""

import socket
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_port_availability():
    """Check if port 7687 is available and listening."""
    print("=" * 60)
    print("Checking Neo4j Desktop Status")
    print("=" * 60)
    
    try:
        # Test if port 7687 is listening
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('localhost', 7687))
        sock.close()
        
        if result == 0:
            print("✅ Port 7687 is listening - Neo4j appears to be running!")
            return True
        else:
            print("❌ Port 7687 is not listening")
            print("   This means Neo4j Desktop database is not started")
            return False
            
    except Exception as e:
        print(f"❌ Error checking port: {e}")
        return False

def show_setup_instructions():
    """Show step-by-step setup instructions."""
    print("\n" + "=" * 60)
    print("Neo4j Desktop Setup Instructions")
    print("=" * 60)
    
    print("""
🚀 To get Neo4j Desktop running:

1. 📱 Open Neo4j Desktop Application
   - Make sure Neo4j Desktop is installed and running
   - You should see the Neo4j Desktop interface

2. 🗂️ Create/Select a Project
   - Click "New" → "Create Project" (if you don't have one)
   - Or select an existing project

3. 🗄️ Create a Database
   - Click "Add" → "Local DBMS"
   - Name: patent-db (or any name)
   - Password: 12345678 (must match .env file)
   - Version: Latest (5.x recommended)
   - Click "Create"

4. ▶️ START the Database
   - Find your database in the project
   - Click the "Start" button (▶️)
   - Wait for green "Active" status

5. ✅ Verify Connection
   - Database should show "Active" with green dot
   - Bolt URL should be: neo4j://localhost:7687

📝 Important Notes:
- The database must be STARTED (not just created)
- Password must match what's in your .env file
- Default username is always "neo4j"
    """)

def check_neo4j_processes():
    """Check if Neo4j processes are running."""
    print("\n" + "=" * 60)
    print("Checking Neo4j Processes")
    print("=" * 60)
    
    try:
        import psutil
        neo4j_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'java' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'neo4j' in cmdline.lower():
                        neo4j_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if neo4j_processes:
            print(f"✅ Found {len(neo4j_processes)} Neo4j process(es) running:")
            for proc in neo4j_processes:
                print(f"   PID: {proc['pid']}, Name: {proc['name']}")
        else:
            print("❌ No Neo4j processes found running")
            print("   Neo4j Desktop may not be started")
            
    except ImportError:
        print("⚠️ psutil not available - cannot check processes")
        print("   Install with: pip install psutil")

def test_simple_connection():
    """Test a simple socket connection."""
    print("\n" + "=" * 60)
    print("Testing Simple Connection")
    print("=" * 60)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('localhost', 7687))
        
        # Try to read some data
        sock.send(b'\x60\x60\xB0\x17')  # Neo4j handshake
        response = sock.recv(4)
        sock.close()
        
        if response:
            print("✅ Successfully connected to Neo4j on port 7687!")
            print(f"   Received response: {response.hex()}")
            return True
        else:
            print("❌ Connected but no response from Neo4j")
            return False
            
    except ConnectionRefusedError:
        print("❌ Connection refused - Neo4j is not running")
        return False
    except socket.timeout:
        print("❌ Connection timeout - Neo4j may be starting up")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def main():
    """Main diagnostic function."""
    print("🔍 Neo4j Desktop Diagnostic Tool")
    print("This will help you get Neo4j Desktop running properly.\n")
    
    # Check if port is listening
    port_ok = check_port_availability()
    
    # Check for Neo4j processes
    check_neo4j_processes()
    
    # Test simple connection
    if port_ok:
        connection_ok = test_simple_connection()
    else:
        connection_ok = False
    
    # Show results
    print("\n" + "=" * 60)
    print("Diagnostic Summary")
    print("=" * 60)
    
    print(f"Port 7687 listening: {'✅ YES' if port_ok else '❌ NO'}")
    print(f"Neo4j connection: {'✅ YES' if connection_ok else '❌ NO'}")
    
    if port_ok and connection_ok:
        print("\n🎉 Neo4j Desktop is running correctly!")
        print("\nNext steps:")
        print("1. Run: python test_connection.py")
        print("2. Run: python load_sample_data.py")
        print("3. Run: python demo.py")
    else:
        show_setup_instructions()
        print("\n🔄 After setting up Neo4j Desktop, run this script again to verify.")

if __name__ == "__main__":
    main()
