#!/usr/bin/env python3
"""
Test different common passwords for Neo4j Desktop.
"""

from neo4j import GraphDatabase
import sys

def test_password(password):
    """Test a specific password."""
    uri = "neo4j://localhost:7687"
    username = "neo4j"
    
    try:
        driver = GraphDatabase.driver(uri, auth=(username, password))
        driver.verify_connectivity()
        driver.close()
        return True
    except Exception as e:
        return False

def main():
    """Test common passwords."""
    print("🔐 Testing Common Neo4j Desktop Passwords")
    print("=" * 50)
    
    # Common passwords people use
    common_passwords = [
        "12345678",    # From our .env file
        "password",    # Very common
        "neo4j",       # Default in some versions
        "admin",       # Common choice
        "123456",      # Simple
        "",            # Empty password
        "test",        # Common test password
        "neo4j123",    # Combination
    ]
    
    for password in common_passwords:
        print(f"Testing password: '{password}'", end=" ... ")
        if test_password(password):
            print("✅ SUCCESS!")
            print(f"\n🎉 Found working password: '{password}'")
            print(f"\nUpdate your .env file:")
            print(f"NEO4J_PASSWORD={password}")
            return password
        else:
            print("❌ Failed")
    
    print("\n❌ None of the common passwords worked.")
    print("\n🔧 Solutions:")
    print("1. Check the password you set in Neo4j Desktop")
    print("2. Reset the password in Neo4j Desktop to '12345678'")
    print("3. Or update the .env file with the correct password")
    
    return None

if __name__ == "__main__":
    main()
