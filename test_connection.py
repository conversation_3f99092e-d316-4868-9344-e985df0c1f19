#!/usr/bin/env python3
"""
Test Neo4j connection with detailed diagnostics.
"""

import sys
import os
import socket
from urllib.parse import urlparse

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.neo4j_connection import Neo4jConnection, Neo4jSettings

def test_dns_resolution():
    """Test if we can resolve the Neo4j hostname."""
    print("=" * 60)
    print("Testing DNS Resolution")
    print("=" * 60)
    
    settings = Neo4jSettings()
    parsed_uri = urlparse(settings.uri)
    hostname = parsed_uri.hostname
    port = parsed_uri.port or 7687
    
    print(f"Hostname: {hostname}")
    print(f"Port: {port}")
    
    try:
        # Test DNS resolution
        ip_address = socket.gethostbyname(hostname)
        print(f"✅ DNS Resolution successful: {ip_address}")
        
        # Test port connectivity
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is reachable")
            return True
        else:
            print(f"❌ Port {port} is not reachable (error code: {result})")
            return False
            
    except socket.gaierror as e:
        print(f"❌ DNS Resolution failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_neo4j_connection():
    """Test Neo4j connection with detailed error reporting."""
    print("\n" + "=" * 60)
    print("Testing Neo4j Connection")
    print("=" * 60)
    
    settings = Neo4jSettings()
    print(f"URI: {settings.uri}")
    print(f"Username: {settings.username}")
    print(f"Database: {settings.database}")
    
    try:
        connection = Neo4jConnection()
        
        # Test basic connectivity
        if connection.verify_connectivity():
            print("✅ Neo4j connection successful!")
            
            # Test a simple query
            with connection as conn:
                with conn.driver.session(database=settings.database) as session:
                    result = session.run("RETURN 'Hello Neo4j!' as message")
                    record = result.single()
                    print(f"✅ Query test successful: {record['message']}")
            
            return True
        else:
            print("❌ Neo4j connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Neo4j connection error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def show_troubleshooting_tips():
    """Show troubleshooting tips for connection issues."""
    print("\n" + "=" * 60)
    print("Troubleshooting Tips")
    print("=" * 60)
    
    print("""
🔧 Common Solutions for Neo4j AuraDB Connection Issues:

1. 📡 Check Instance Status:
   - Ensure your Neo4j AuraDB instance is RUNNING (not paused)
   - Check the Neo4j Aura console for instance status
   - Restart the instance if it's paused

2. 🌐 Network Connectivity:
   - Check your internet connection
   - Verify firewall settings allow outbound connections on port 7687
   - Try connecting from a different network if possible

3. 🔐 Credentials:
   - Verify username and password are correct
   - Check if the password has special characters that need escaping
   - Ensure the database name is correct (usually 'neo4j')

4. 🔒 SSL/TLS:
   - AuraDB requires SSL connection (neo4j+s://)
   - Ensure your system has up-to-date certificates
   - Try updating the neo4j driver: pip install --upgrade neo4j

5. 🕒 Timeout Issues:
   - AuraDB instances may auto-pause after inactivity
   - Try accessing the instance through Neo4j Browser first
   - Wait a few minutes for the instance to fully start

6. 📍 Region/Location:
   - Check if the instance is in a different region
   - Verify the correct connection URI from Aura console
    """)

def main():
    """Main diagnostic function."""
    print("🔍 Neo4j Connection Diagnostics")
    print("This script will help diagnose connection issues.\n")
    
    # Test DNS resolution first
    dns_ok = test_dns_resolution()
    
    # Test Neo4j connection
    neo4j_ok = test_neo4j_connection()
    
    # Show results summary
    print("\n" + "=" * 60)
    print("Diagnostic Summary")
    print("=" * 60)
    
    print(f"DNS Resolution: {'✅ OK' if dns_ok else '❌ FAILED'}")
    print(f"Neo4j Connection: {'✅ OK' if neo4j_ok else '❌ FAILED'}")
    
    if not dns_ok or not neo4j_ok:
        show_troubleshooting_tips()
    else:
        print("\n🎉 All tests passed! Your Neo4j connection is working.")
        print("\nNext steps:")
        print("1. Run: python load_sample_data.py (to load sample patent data)")
        print("2. Run: python demo.py (to test the full application)")

if __name__ == "__main__":
    main()
