#!/usr/bin/env python3
"""
Test natural language queries with the Patent Similarity Search application.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data.cypher_generator import CypherQueryGenerator

def test_queries():
    """Test various natural language queries."""
    print("🚀 Testing Natural Language to Cypher Query Generation")
    print("=" * 60)
    
    # Initialize the query generator
    try:
        generator = CypherQueryGenerator()
        print("✅ CypherQueryGenerator initialized successfully!")
    except Exception as e:
        print(f"❌ Error initializing generator: {e}")
        return
    
    # Test queries
    test_questions = [
        "Show me all patents",
        "Find patents invented by Dr. <PERSON>",
        "What patents cite other patents?",
        "Show me patents similar to each other",
        "Find all inventors and their companies",
        "Which patents are about machine learning?",
    ]
    
    print(f"\n🔍 Testing {len(test_questions)} natural language questions:\n")
    
    for i, question in enumerate(test_questions, 1):
        print(f"Question {i}: {question}")
        try:
            query = generator.generate_query(question)
            print(f"Generated Cypher: {query}")
            print("✅ Success!")
        except Exception as e:
            print(f"❌ Error: {e}")
        print("-" * 50)

def main():
    """Main function."""
    test_queries()
    
    print("\n🎉 Query generation testing complete!")
    print("\nYour Patent Similarity Search application is fully functional!")
    print("\nNext steps:")
    print("1. Try more complex queries")
    print("2. Execute queries against your database")
    print("3. Explore patent relationships and similarities")

if __name__ == "__main__":
    main()
