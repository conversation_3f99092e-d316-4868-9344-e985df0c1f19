#!/usr/bin/env python3
"""
Example usage of the Patent Similarity Search application.
This script demonstrates how to use the main components without requiring a live database.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.neo4j_schema import GraphSchema

def demonstrate_schema_functionality():
    """Demonstrate the GraphSchema functionality."""
    print("=" * 60)
    print("Patent Similarity Search - Example Usage")
    print("=" * 60)
    
    # Create a sample schema that might exist in a patent database
    sample_schema = GraphSchema(
        node_labels=[
            "Patent", 
            "Inventor", 
            "Company", 
            "Technology", 
            "Classification"
        ],
        relationship_types=[
            "INVENTED_BY",
            "CITES", 
            "ASSIGNED_TO",
            "BELONGS_TO_CLASS",
            "SIMILAR_TO",
            "USES_TECHNOLOGY"
        ],
        property_keys=[
            "patent_id",
            "title", 
            "abstract",
            "filing_date",
            "grant_date",
            "inventor_name",
            "company_name",
            "classification_code",
            "similarity_score",
            "technology_type"
        ]
    )
    
    print("\n📊 Sample Patent Database Schema:")
    print("This shows what the schema might look like in a real patent database.\n")
    sample_schema.display()
    
    return sample_schema

def demonstrate_query_examples():
    """Show example Cypher queries that could be generated."""
    print("\n" + "=" * 60)
    print("Example Cypher Queries for Patent Analysis")
    print("=" * 60)
    
    example_queries = [
        {
            "question": "Find all patents by a specific inventor",
            "cypher": """
MATCH (i:Inventor {inventor_name: 'John Smith'})-[:INVENTED_BY]-(p:Patent)
RETURN p.title, p.patent_id, p.filing_date
ORDER BY p.filing_date DESC
            """.strip()
        },
        {
            "question": "Find patents similar to a given patent",
            "cypher": """
MATCH (p1:Patent {patent_id: 'US1234567'})-[r:SIMILAR_TO]-(p2:Patent)
WHERE r.similarity_score > 0.8
RETURN p2.title, p2.patent_id, r.similarity_score
ORDER BY r.similarity_score DESC
            """.strip()
        },
        {
            "question": "Find the most cited patents in a technology area",
            "cypher": """
MATCH (p:Patent)-[:BELONGS_TO_CLASS]->(c:Classification {classification_code: 'H04L'})
MATCH (p)<-[cites:CITES]-(:Patent)
WITH p, count(cites) as citation_count
RETURN p.title, p.patent_id, citation_count
ORDER BY citation_count DESC
LIMIT 10
            """.strip()
        },
        {
            "question": "Find collaboration networks between inventors",
            "cypher": """
MATCH (i1:Inventor)-[:INVENTED_BY]-(p:Patent)-[:INVENTED_BY]-(i2:Inventor)
WHERE i1 <> i2
WITH i1, i2, count(p) as collaborations
WHERE collaborations > 1
RETURN i1.inventor_name, i2.inventor_name, collaborations
ORDER BY collaborations DESC
            """.strip()
        }
    ]
    
    for i, example in enumerate(example_queries, 1):
        print(f"\n🔍 Example {i}: {example['question']}")
        print("Generated Cypher Query:")
        print("```cypher")
        print(example['cypher'])
        print("```")

def demonstrate_application_features():
    """Demonstrate the key features of the application."""
    print("\n" + "=" * 60)
    print("Key Features of Patent Similarity Search")
    print("=" * 60)
    
    features = [
        {
            "feature": "🔍 Natural Language to Cypher",
            "description": "Convert natural language questions into Cypher queries using LLM"
        },
        {
            "feature": "📊 Schema Exploration", 
            "description": "Automatically discover and display Neo4j database schema"
        },
        {
            "feature": "🔗 Neo4j Integration",
            "description": "Seamless connection and querying of Neo4j graph databases"
        },
        {
            "feature": "🧪 Comprehensive Testing",
            "description": "Full test suite with mocked dependencies for reliable development"
        },
        {
            "feature": "⚙️ Configurable LLM",
            "description": "Support for different LLM providers (OpenAI, Groq, etc.)"
        },
        {
            "feature": "📈 Patent Analytics",
            "description": "Specialized for patent data analysis and similarity search"
        }
    ]
    
    print("\nThis application provides the following capabilities:\n")
    for feature in features:
        print(f"{feature['feature']}")
        print(f"   {feature['description']}\n")

def show_setup_instructions():
    """Show setup instructions for running with real data."""
    print("=" * 60)
    print("Setup Instructions for Real Usage")
    print("=" * 60)
    
    print("""
To use this application with real patent data:

1. 📦 Install Neo4j Database:
   - Download and install Neo4j Desktop or Community Edition
   - Start a new database instance
   - Note the connection details (URI, username, password)

2. 🔧 Configure Environment:
   - Update the .env file with your Neo4j credentials
   - Add your LLM API key (OpenAI, Groq, etc.)

3. 📊 Load Patent Data:
   - Import patent data into Neo4j using LOAD CSV or other methods
   - Ensure proper node labels and relationships are created
   - Example data structure:
     * (:Patent {patent_id, title, abstract, filing_date})
     * (:Inventor {name, affiliation})
     * (:Patent)-[:INVENTED_BY]->(:Inventor)
     * (:Patent)-[:CITES]->(:Patent)

4. 🚀 Run the Application:
   - Use the demo.py script to test connectivity
   - Import and use the CypherQueryGenerator class
   - Query your patent database with natural language

5. 🧪 Run Tests:
   - Execute: pytest tests/ -v
   - All tests should pass with proper mocking
    """)

def main():
    """Main demonstration function."""
    print("🚀 Welcome to Patent Similarity Search!")
    print("This example demonstrates the application's capabilities.\n")
    
    # Demonstrate schema functionality
    schema = demonstrate_schema_functionality()
    
    # Show example queries
    demonstrate_query_examples()
    
    # Show application features
    demonstrate_application_features()
    
    # Show setup instructions
    show_setup_instructions()
    
    print("\n" + "=" * 60)
    print("Example Complete!")
    print("=" * 60)
    print("\nNext Steps:")
    print("1. Set up a Neo4j database with patent data")
    print("2. Configure your .env file with database credentials")
    print("3. Run python demo.py to test the live connection")
    print("4. Start building your patent analysis queries!")

if __name__ == "__main__":
    main()
