# Neo4j Desktop Setup Guide

## 🚀 Quick Setup Steps

### 1. Start Neo4j Desktop
- Open **Neo4j Desktop** application
- Make sure it's running (should see the Neo4j Desktop interface)

### 2. Create a New Project (if needed)
- Click **"New"** → **"Create Project"**
- Give it a name like "Patent Similarity Search"

### 3. Create a Database
- In your project, click **"Add"** → **"Local DBMS"**
- **Name**: `patent-db` (or any name you prefer)
- **Password**: `12345678` (must match your .env file)
- **Version**: Use the latest version (5.x recommended)
- Click **"Create"**

### 4. Start the Database
- Find your database in the project
- Click the **"Start"** button (▶️)
- Wait for status to show **"Active"** (green dot)

### 5. Verify Connection Details
- Click the **"..."** menu next to your database
- Select **"Manage"**
- Go to **"Details"** tab
- Verify:
  - **Bolt URL**: `neo4j://localhost:7687`
  - **Username**: `neo4j`
  - **Password**: `12345678`

## 🔧 Common Issues & Solutions

### Issue: "Connection Refused" (Port 7687 not reachable)
**Solution:**
- Ensure Neo4j Desktop is running
- Make sure your database is **Started** (green "Active" status)
- Check Windows Firewall isn't blocking port 7687

### Issue: "Authentication Failed"
**Solution:**
- Verify password in Neo4j Desktop matches .env file
- Default username is always `neo4j`
- Reset password if needed:
  1. Stop the database
  2. Click "..." → "Manage" → "Administration"
  3. Reset password to `12345678`

### Issue: "Database Not Found"
**Solution:**
- Ensure database name in .env matches actual database
- Default database name is usually `neo4j`
- Check in Neo4j Desktop under database details

## 🧪 Test Your Setup

### Step 1: Test with Neo4j Browser
1. In Neo4j Desktop, click **"Open"** next to your running database
2. This opens Neo4j Browser
3. Run this query: `RETURN "Hello Neo4j!" as message`
4. If this works, your database is running correctly

### Step 2: Test with Our Application
```bash
# Test connection
python test_connection.py

# Should show:
# ✅ DNS Resolution successful: 127.0.0.1
# ✅ Port 7687 is reachable
# ✅ Neo4j connection successful!
```

### Step 3: Load Sample Data
```bash
# Load patent data
python load_sample_data.py

# Should create sample patents, inventors, and relationships
```

### Step 4: Run Full Demo
```bash
# Test the complete application
python demo.py

# Should show successful connection and schema exploration
```

## 📊 Expected Results After Setup

When everything is working, you should see:

```
🚀 Patent Similarity Search - Demo Application
============================================================
Testing Neo4j Database Connection
============================================================
✅ Successfully connected to Neo4j database!

============================================================
Exploring Database Schema
============================================================
📊 Database Schema Information:
Node Labels:
- Patent
- Inventor
- Company

Relationship Types:
- INVENTED_BY
- CITES
- SIMILAR_TO
- AFFILIATED_WITH

Property Keys (across all nodes and relationships):
- patent_id
- title
- abstract
- filing_date
- grant_date
- inventor_name
- company_name
- similarity_score
```

## 🔄 Alternative: Use Different Port

If port 7687 is busy, you can:

1. **Change Neo4j port in Desktop:**
   - Stop your database
   - Go to "..." → "Manage" → "Settings"
   - Change `dbms.connector.bolt.listen_address=:7687` to `:7688`
   - Start database

2. **Update .env file:**
   ```env
   NEO4J_URI=neo4j://localhost:7688
   ```

## 📞 Need Help?

If you're still having issues:

1. **Check Neo4j Desktop logs:**
   - Click "..." → "Manage" → "Logs"
   - Look for error messages

2. **Verify system requirements:**
   - Java 11+ installed
   - Sufficient RAM (4GB+ recommended)
   - No other services using port 7687

3. **Try a fresh database:**
   - Create a new database with a different name
   - Use a simple password like `password`
   - Update .env accordingly

## 🎯 Next Steps After Setup

Once connected:
1. ✅ Load sample data: `python load_sample_data.py`
2. ✅ Test queries: `python demo.py`
3. ✅ Try natural language queries with your LLM API key
4. ✅ Explore the patent similarity features!
