#!/usr/bin/env python3
"""
Demo script for Patent Similarity Search application.
This script demonstrates the main functionality of the application.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.neo4j_schema import Neo4jSchemaExplorer
from src.data.cypher_generator import CypherQueryGenerator

def test_database_connection():
    """Test the Neo4j database connection."""
    print("=" * 60)
    print("Testing Neo4j Database Connection")
    print("=" * 60)
    
    try:
        explorer = Neo4jSchemaExplorer()
        
        # Test connection
        if explorer._connection.verify_connectivity():
            print("✅ Successfully connected to Neo4j database!")
            return True
        else:
            print("❌ Failed to connect to Neo4j database.")
            print("Please ensure Neo4j is running and credentials are correct.")
            return False
            
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure Neo4j is running on your system")
        print("2. Check your .env file for correct credentials")
        print("3. Verify the Neo4j URI is correct")
        return False

def explore_database_schema():
    """Explore and display the database schema."""
    print("\n" + "=" * 60)
    print("Exploring Database Schema")
    print("=" * 60)
    
    try:
        explorer = Neo4jSchemaExplorer()
        schema = explorer.get_schema()
        
        print("\n📊 Database Schema Information:")
        schema.display()
        
        return schema
        
    except Exception as e:
        print(f"❌ Error exploring schema: {e}")
        return None

def test_cypher_generation():
    """Test the Cypher query generation functionality."""
    print("\n" + "=" * 60)
    print("Testing Cypher Query Generation")
    print("=" * 60)
    
    try:
        generator = CypherQueryGenerator()
        
        # Test with a simple question
        test_question = "Show me all patents"
        print(f"\n🔍 Question: {test_question}")
        
        query = generator.generate_query(test_question)
        print(f"📝 Generated Cypher Query:")
        print(f"   {query}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating Cypher query: {e}")
        print("\nNote: This requires a valid LLM API key in the .env file")
        return False

def main():
    """Main demo function."""
    print("🚀 Patent Similarity Search - Demo Application")
    print("This demo will test the main components of the application.\n")
    
    # Test database connection
    if not test_database_connection():
        print("\n⚠️  Database connection failed. Some features may not work.")
        print("You can still explore the code structure and run tests.")
        return
    
    # Explore database schema
    schema = explore_database_schema()
    
    # Test Cypher generation (if schema is available)
    if schema and (schema.node_labels or schema.relationship_types):
        test_cypher_generation()
    else:
        print("\n⚠️  No data found in database. Skipping Cypher generation test.")
        print("To fully test the application, you'll need to load some patent data into Neo4j.")
    
    print("\n" + "=" * 60)
    print("Demo Complete!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Load patent data into your Neo4j database")
    print("2. Run the tests: pytest tests/ -v")
    print("3. Explore the source code in the src/ directory")
    print("4. Check the README.md for more detailed usage instructions")

if __name__ == "__main__":
    main()
