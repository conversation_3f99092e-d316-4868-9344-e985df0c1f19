#!/usr/bin/env python3
"""
Load sample patent data into Neo4j database.
This script creates a sample patent dataset for testing the application.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.neo4j_connection import Neo4jConnection

def create_sample_patent_data():
    """Create sample patent data in Neo4j."""
    
    # Sample patent data
    sample_data = {
        'patents': [
            {
                'patent_id': 'US10123456',
                'title': 'Machine Learning System for Patent Analysis',
                'abstract': 'A system and method for analyzing patent documents using machine learning algorithms to identify similar patents and predict patent approval likelihood.',
                'filing_date': '2020-01-15',
                'grant_date': '2022-03-20',
                'classification': 'G06F17/30'
            },
            {
                'patent_id': 'US10234567', 
                'title': 'Neural Network Architecture for Document Similarity',
                'abstract': 'An improved neural network architecture specifically designed for computing semantic similarity between technical documents.',
                'filing_date': '2020-05-10',
                'grant_date': '2022-08-15',
                'classification': 'G06N3/08'
            },
            {
                'patent_id': 'US10345678',
                'title': 'Graph Database System for Patent Search',
                'abstract': 'A graph-based database system optimized for storing and searching patent relationships and citations.',
                'filing_date': '2019-12-01',
                'grant_date': '2021-11-30',
                'classification': 'G06F16/901'
            },
            {
                'patent_id': 'US10456789',
                'title': 'Automated Patent Prior Art Discovery',
                'abstract': 'An automated system for discovering prior art relevant to patent applications using natural language processing.',
                'filing_date': '2021-03-22',
                'grant_date': '2023-01-10',
                'classification': 'G06F40/30'
            }
        ],
        'inventors': [
            {'name': 'Dr. Alice Johnson', 'affiliation': 'TechCorp Research'},
            {'name': 'Prof. Bob Smith', 'affiliation': 'University of Technology'},
            {'name': 'Dr. Carol Williams', 'affiliation': 'Innovation Labs'},
            {'name': 'Dr. David Brown', 'affiliation': 'TechCorp Research'},
            {'name': 'Dr. Eve Davis', 'affiliation': 'StartupAI Inc'}
        ],
        'companies': [
            {'name': 'TechCorp Research', 'type': 'Corporation'},
            {'name': 'University of Technology', 'type': 'Academic'},
            {'name': 'Innovation Labs', 'type': 'Research Institute'},
            {'name': 'StartupAI Inc', 'type': 'Startup'}
        ]
    }
    
    # Relationships data
    relationships = [
        # Patent-Inventor relationships
        ('US10123456', 'Dr. Alice Johnson'),
        ('US10123456', 'Dr. David Brown'),
        ('US10234567', 'Prof. Bob Smith'),
        ('US10234567', 'Dr. Eve Davis'),
        ('US10345678', 'Dr. Carol Williams'),
        ('US10456789', 'Dr. Alice Johnson'),
        ('US10456789', 'Prof. Bob Smith'),
        
        # Patent citations (patent_id, cited_patent_id)
        ('US10234567', 'US10123456'),  # Neural Network cites ML System
        ('US10456789', 'US10123456'),  # Prior Art Discovery cites ML System
        ('US10456789', 'US10345678'),  # Prior Art Discovery cites Graph DB
    ]
    
    return sample_data, relationships

def load_data_to_neo4j():
    """Load the sample data into Neo4j database."""
    print("=" * 60)
    print("Loading Sample Patent Data into Neo4j")
    print("=" * 60)
    
    try:
        with Neo4jConnection() as conn:
            if not conn.verify_connectivity():
                print("❌ Cannot connect to Neo4j database")
                return False
            
            sample_data, relationships = create_sample_patent_data()
            
            with conn.driver.session() as session:
                # Clear existing data (optional)
                print("🧹 Clearing existing data...")
                session.run("MATCH (n) DETACH DELETE n")
                
                # Create Patents
                print("📄 Creating patent nodes...")
                for patent in sample_data['patents']:
                    query = """
                    CREATE (p:Patent {
                        patent_id: $patent_id,
                        title: $title,
                        abstract: $abstract,
                        filing_date: date($filing_date),
                        grant_date: date($grant_date),
                        classification: $classification
                    })
                    """
                    session.run(query, patent)
                
                # Create Inventors
                print("👨‍🔬 Creating inventor nodes...")
                for inventor in sample_data['inventors']:
                    query = """
                    CREATE (i:Inventor {
                        name: $name,
                        affiliation: $affiliation
                    })
                    """
                    session.run(query, inventor)
                
                # Create Companies
                print("🏢 Creating company nodes...")
                for company in sample_data['companies']:
                    query = """
                    CREATE (c:Company {
                        name: $name,
                        type: $type
                    })
                    """
                    session.run(query, company)
                
                # Create Patent-Inventor relationships
                print("🔗 Creating patent-inventor relationships...")
                for patent_id, inventor_name in relationships[:7]:  # First 7 are patent-inventor
                    query = """
                    MATCH (p:Patent {patent_id: $patent_id})
                    MATCH (i:Inventor {name: $inventor_name})
                    CREATE (p)-[:INVENTED_BY]->(i)
                    """
                    session.run(query, {'patent_id': patent_id, 'inventor_name': inventor_name})
                
                # Create Inventor-Company relationships
                print("🔗 Creating inventor-company relationships...")
                for inventor in sample_data['inventors']:
                    query = """
                    MATCH (i:Inventor {name: $inventor_name})
                    MATCH (c:Company {name: $company_name})
                    CREATE (i)-[:AFFILIATED_WITH]->(c)
                    """
                    session.run(query, {
                        'inventor_name': inventor['name'], 
                        'company_name': inventor['affiliation']
                    })
                
                # Create Patent citation relationships
                print("📚 Creating patent citation relationships...")
                for citing_patent, cited_patent in relationships[7:]:  # Last 3 are citations
                    query = """
                    MATCH (p1:Patent {patent_id: $citing_patent})
                    MATCH (p2:Patent {patent_id: $cited_patent})
                    CREATE (p1)-[:CITES]->(p2)
                    """
                    session.run(query, {
                        'citing_patent': citing_patent, 
                        'cited_patent': cited_patent
                    })
                
                # Add some similarity relationships (simulated)
                print("🔍 Creating similarity relationships...")
                similarity_data = [
                    ('US10123456', 'US10234567', 0.85),  # ML System similar to Neural Network
                    ('US10123456', 'US10456789', 0.72),  # ML System similar to Prior Art Discovery
                    ('US10234567', 'US10456789', 0.68),  # Neural Network similar to Prior Art Discovery
                ]
                
                for patent1, patent2, score in similarity_data:
                    query = """
                    MATCH (p1:Patent {patent_id: $patent1})
                    MATCH (p2:Patent {patent_id: $patent2})
                    CREATE (p1)-[:SIMILAR_TO {similarity_score: $score}]->(p2)
                    CREATE (p2)-[:SIMILAR_TO {similarity_score: $score}]->(p1)
                    """
                    session.run(query, {
                        'patent1': patent1, 
                        'patent2': patent2, 
                        'score': score
                    })
                
                # Get summary statistics
                print("\n📊 Data loading complete! Summary:")
                
                stats_queries = [
                    ("Patents", "MATCH (p:Patent) RETURN count(p) as count"),
                    ("Inventors", "MATCH (i:Inventor) RETURN count(i) as count"),
                    ("Companies", "MATCH (c:Company) RETURN count(c) as count"),
                    ("Invention relationships", "MATCH ()-[r:INVENTED_BY]->() RETURN count(r) as count"),
                    ("Citation relationships", "MATCH ()-[r:CITES]->() RETURN count(r) as count"),
                    ("Similarity relationships", "MATCH ()-[r:SIMILAR_TO]->() RETURN count(r) as count"),
                ]
                
                for label, query in stats_queries:
                    result = session.run(query).single()
                    print(f"  • {label}: {result['count']}")
                
                print("\n✅ Sample patent data loaded successfully!")
                return True
                
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Patent Data Loader")
    print("This script loads sample patent data for testing.\n")
    
    success = load_data_to_neo4j()
    
    if success:
        print("\n" + "=" * 60)
        print("Next Steps")
        print("=" * 60)
        print("1. Run: python demo.py (to test the application)")
        print("2. Try some example queries in Neo4j Browser:")
        print("   • MATCH (p:Patent) RETURN p LIMIT 5")
        print("   • MATCH (p:Patent)-[:INVENTED_BY]->(i:Inventor) RETURN p.title, i.name")
        print("   • MATCH (p1:Patent)-[s:SIMILAR_TO]->(p2:Patent) RETURN p1.title, p2.title, s.similarity_score")
    else:
        print("\n❌ Data loading failed. Please check your Neo4j connection.")

if __name__ == "__main__":
    main()
