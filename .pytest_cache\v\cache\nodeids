["tests/test_cypher_generator.py::test_cypher_query_generator_initialization", "tests/test_cypher_generator.py::test_format_schema", "tests/test_cypher_generator.py::test_generate_query_failure", "tests/test_cypher_generator.py::test_generate_query_success", "tests/test_neo4j_schema.py::test_connection_failure", "tests/test_neo4j_schema.py::test_get_node_properties_success", "tests/test_neo4j_schema.py::test_get_relationship_properties_success", "tests/test_neo4j_schema.py::test_get_schema_success", "tests/test_neo4j_schema.py::test_graph_schema_display"]